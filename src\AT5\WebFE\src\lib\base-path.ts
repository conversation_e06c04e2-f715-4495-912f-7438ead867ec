import { appConfig } from "@/config/app";

/**
 * Get the application base path
 */
export function getBasePath(): string {
	return appConfig.basePath;
}

/**
 * Get the base path with trailing slash for Vite configuration
 */
export function getBasePathWithSlash(): string {
	const basePath = getBasePath();
	return basePath.endsWith('/') ? basePath : `${basePath}/`;
}

/**
 * Get the base path without leading slash for router basename
 */
export function getRouterBasename(): string {
	const basePath = getBasePath();
	return basePath.startsWith('/') ? basePath : `/${basePath}`;
}



/**
 * Check if the current environment uses a base path
 */
export function hasBasePath(): boolean {
	return getBasePath() !== '/' && getBasePath() !== '';
}
